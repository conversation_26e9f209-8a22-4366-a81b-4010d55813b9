/**
 * OTA渠道映射配置文件
 * 管理所有登录账号对应的默认OTA渠道和可选项
 * <AUTHOR> Assistant
 * @date 2024-12-19
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 账号ID到OTA渠道配置的映射表
     * 每个账号包含默认渠道和可选渠道列表
     * 除了特殊配置的账号外，其他账号默认使用全部OTA渠道
     */
    const OTA_CHANNEL_MAPPING = {
        // === 特殊配置账号 ===
        // JRCoach - 特殊配置账号，仅显示当前配置的OTA渠道，不显示其他通用渠道
        2666: {
            default: "JR Coach Credit",
            options: [
                "JR Coach Credit"
            ]
        },
        
        // Chong Dealer - 特殊配置账号，仅显示当前配置的OTA渠道
        420: {
            default: "Chong Dealer",
            options: [
                "Chong Dealer"
            ]
        },
        
        // SMW - 特殊配置账号，仅显示当前配置的OTA渠道
        37: {
            default: "Heycar",
            options: [
                "Heycar"
            ]
        }
        
        // === 其他账号列表 - 默认使用所有OTA渠道 ===
        // 如需为特定账号配置专门的默认渠道，取消注释并修改配置：
        
        // 1: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // Super Admin
        // 89: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // GMH Sabah
        // 310: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // Jcy
        // 311: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // opAnnie
        // 312: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // opVenus
        // 313: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // opEric
        // 342: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // SMW Wendy
        // 343: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // SMW XiaoYu
        // 421: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // josua
        // 428: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // Gomyhire Yong
        // 533: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // xhs
        // 550: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // test
        // 622: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // CsBob
        // 777: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // 空空
        // 812: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // 淼淼
        // 856: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // GMH Ashley
        // 907: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // OP XINYIN
        // 1043: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // Billy Yong close
        // 1047: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // OP QiJun
        // 1181: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // Op Karen
        // 1201: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // KK Lucas
        // 1223: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // Chong admin
        // 1652: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // CSteam Swee Qing
        // 1832: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // GMH SG William
        // 2050: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // agent victor
        // 2085: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // CSteam Tze Ying
        // 2141: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // SMW Nas
        // 2142: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // SMW Wen
        // 2248: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // GMH Shi Wei
        // 2249: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // Skymirror jetty
        // 2340: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // GMH JingSoon
        // 2358: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // GMH Zilok
        // 2446: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // UCSI - Cheras
        // 2503: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }, // GMH Veron
        // 2595: { default: "渠道名称", options: ["渠道名称", ...COMMON_OTA_CHANNELS] }  // Admin Pua
    };

    /**
     * 通用OTA渠道选项列表
     * 当用户没有专门配置时可以使用的通用选项
     * 基于OTA List.md中的完整渠道列表
     */
    const COMMON_OTA_CHANNELS = [
        "Klook West Malaysia",
        "Heycar",
        "Kkday",
        "Ctrip West Malaysia",
        "SMW Eric",
        "Fliggy",
        "Jing Ge",
        "Smw Wilson",
        "YenNei",
        "Traveloka",
        "GMH Sabah",
        "Reward",
        "Smw Josua",
        "Smw Jcyap",
        "Smw Vivian Lim",
        "Smw Wendy",
        "Smw Annie",
        "GMH Terry",
        "SMW Xiaohongshu",
        "M.I.C.E Tour",
        "UCSI - Cheras",
        "UCSI - Port Dickson",
        "MapleHome - The Robertson KL",
        "Sabah Adventure",
        "全景旅游",
        "MapleHome - Swiss Garden Kuala Lumpur",
        "MapleHome - D'Majestic Premier Suites Kuala Lumpur",
        "MapleHome- Chambers Premier Suites Kuala Lumpur",
        "MapleHome - Geo38 Premier Suites Kuala Lumpur",
        "MapleHome - The Apple Premier Suites Melaka",
        "MapleHome - Amber Cove Premier Suites Melaka",
        "Chong Dealer",
        "EHTT 徐杰",
        "Joydeer",
        "KL Eric",
        "WelcomePickups Sabah",
        "WelcomePickups West Malaysia",
        "BNI Member",
        "GoMyHire - KL",
        "Co-operate Stan",
        "GMH Ms Yong",
        "Mozio",
        "The Maple Suite - Bukit Bintang",
        "PS Member",
        "PS Badminton Team & Family",
        "7deer Travel",
        "Columbia",
        "Asia Trail",
        "Bob",
        "Sim Card",
        "SIM Card + Paging",
        "Paging",
        "The Pearl Kuala Lumpur Hotel",
        "携程专车",
        "Le Méridien Putrajaya",
        "Gomyhire Pohchengfatt",
        "789 Genting",
        "The Little Series",
        "Syn",
        "Bintang Collectionz Hotel",
        "GoMyHire Webpage",
        "Jing Ge Htp",
        "ReSkill",
        "JC666",
        "Thousand Travel",
        "CEO Chaffer Premium",
        "GMH Ashley",
        "GMH Calvin",
        "Pg Sue",
        "Rental",
        "Rent To Own",
        "Penalty",
        "GMH Ads",
        "ONE18 Boutique Hotel",
        "Wiracle Vincent",
        "GMH May",
        "KK Lucas",
        "GMH Daniel Fong",
        "GMH BNI",
        "GMH SQ",
        "GMH Jiahui",
        "GMH Vikki",
        "上海佳禾",
        "Pg Afzan",
        "Link Center (SBH)",
        "ATV Borneo Sabah",
        "Agent Victor",
        "SMW Whatsapp",
        "SMW Agent",
        "GMH Qijun",
        "GMH Venus",
        "GMH Karen",
        "SMW Walk In",
        "Good Earth Travel",
        "Hotel - Secret Garden Homestay",
        "HTP - 空港嘉华",
        "Hotel - Leshore Hotel",
        "Hotel - VI Boutique",
        "Hotel - East Sun Hotel",
        "Hotel - Padibox Homestay",
        "Hotel - Padi Sentral Homestay",
        "B2B Lewis",
        "Klook Singapore",
        "Ctrip API",
        "Want To Eat Restaurant",
        "GMH Cynthia B10",
        "GMH Cynthia",
        "Driver Own Job",
        "GMH Jing Soon",
        "Smartryde HTP",
        "GMH Driver",
        "B TN Holiday Sdn Bhd-Eunice",
        "携程商铺 - CN",
        "diagnosis-test",
        "GMH Xiaoxuan",
        "JR Coast Credit",
        "KTMB"
    ];

    /**
     * 获取指定用户ID的OTA渠道配置
     * @param {number|string} userId - 用户ID
     * @returns {object|null} 包含default和options的配置对象，如果未找到则返回null
     */
    function getOtaChannelConfig(userId) {
        // 转换为数字类型进行查找
        const numericId = parseInt(userId);
        const stringId = String(userId);
        
        // 查找配置
        const config = OTA_CHANNEL_MAPPING[numericId] || OTA_CHANNEL_MAPPING[stringId] || null;
        
        // 调试日志
        console.log('获取OTA渠道配置', {
            originalUserId: userId,
            numericId: numericId,
            stringId: stringId,
            foundConfig: config,
            availableKeys: Object.keys(OTA_CHANNEL_MAPPING)
        });
        
        return config;
    }

    /**
     * 获取指定用户的默认OTA渠道
     * @param {number|string} userId - 用户ID
     * @returns {string|null} 默认OTA渠道名称，如果未找到则返回null
     */
    function getDefaultOtaChannel(userId) {
        const config = getOtaChannelConfig(userId);
        return config ? config.default : null;
    }

    /**
     * 获取指定用户的OTA渠道选项列表
     * @param {number|string} userId - 用户ID
     * @returns {string[]} OTA渠道选项数组
     */
    function getOtaChannelOptions(userId) {
        const config = getOtaChannelConfig(userId);
        return config ? config.options : COMMON_OTA_CHANNELS;
    }

    /**
     * 获取所有已配置的用户ID列表
     * @returns {number[]} 用户ID数组
     */
    function getConfiguredUserIds() {
        return Object.keys(OTA_CHANNEL_MAPPING).map(id => parseInt(id));
    }

    /**
     * 添加或更新用户的OTA渠道配置
     * @param {number|string} userId - 用户ID
     * @param {string} defaultChannel - 默认渠道
     * @param {string[]} options - 可选渠道列表
     */
    function setOtaChannelConfig(userId, defaultChannel, options = []) {
        const numericId = parseInt(userId);
        OTA_CHANNEL_MAPPING[numericId] = {
            default: defaultChannel,
            options: options.length > 0 ? options : [defaultChannel, ...COMMON_OTA_CHANNELS]
        };
    }

    /**
     * 获取所有OTA渠道配置的摘要信息
     * @returns {object} 包含统计信息的对象
     */
    function getOtaConfigSummary() {
        const userIds = getConfiguredUserIds();
        const totalUsers = userIds.length;
        const allChannels = new Set();
        
        userIds.forEach(userId => {
            const config = getOtaChannelConfig(userId);
            if (config) {
                allChannels.add(config.default);
                config.options.forEach(option => allChannels.add(option));
            }
        });
        
        return {
            totalConfiguredUsers: totalUsers,
            totalUniqueChannels: allChannels.size,
            configuredUserIds: userIds,
            allChannels: Array.from(allChannels).sort()
        };
    }

    // 暴露到OTA命名空间
    window.OTA.otaChannelMapping = {
        getConfig: getOtaChannelConfig,
        getDefault: getDefaultOtaChannel,
        getOptions: getOtaChannelOptions,
        getConfiguredUsers: getConfiguredUserIds,
        setConfig: setOtaChannelConfig,
        getSummary: getOtaConfigSummary,
        commonChannels: COMMON_OTA_CHANNELS,
        mapping: OTA_CHANNEL_MAPPING
    };

    // 向后兼容：暴露主要函数到全局
    window.getOtaChannelConfig = getOtaChannelConfig;
    window.getDefaultOtaChannel = getDefaultOtaChannel;
    window.getOtaChannelOptions = getOtaChannelOptions;

    // 控制台输出配置摘要（仅在开发模式下）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('OTA渠道映射配置已加载:', getOtaConfigSummary());
    }

})();