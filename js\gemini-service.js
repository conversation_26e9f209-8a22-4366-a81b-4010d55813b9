/**
 * Gemini AI服务模块
 * 负责与Google Gemini API的交互，提供订单内容智能解析功能
 * 支持实时分析和批量处理
 * 重构为传统script标签加载方式
 *
 * 配置信息：
 * - 模型版本：Gemini 2.0 Flash (gemini-2.5-flash-lite-preview-06-17)
 * - API密钥：内嵌配置（个人自用项目）
 * - 智能ID填充：基于api return id list.md数据映射
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块（延迟获取以确保加载顺序）
    function getAppState() {
        return window.OTA.appState || window.appState;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

class GeminiService {
    constructor() {
        // 内嵌API密钥配置（个人自用项目，忽略安全警告）
        this.apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';

        // 更新为Gemini 2.0 Flash模型
        this.modelVersion = 'gemini-2.5-flash-lite-preview-06-17';
        this.baseURL = `https://generativelanguage.googleapis.com/v1beta/models/${this.modelVersion}:generateContent`;
        this.timeout = 30000;
        
        // 实时分析配置
        this.realtimeConfig = {
            enabled: true,
            debounceDelay: 1500, // 1.5秒防抖延迟
            minInputLength: 20, // 最小输入长度才触发分析
            maxRetries: 2, // 最大重试次数
            confidenceThreshold: 0.3 // 最低置信度阈值
        };
        
        // 分析状态管理
        this.analysisState = {
            isAnalyzing: false,
            lastAnalyzedText: '',
            currentRequest: null,
            analysisHistory: []
        };

        // 智能ID填充映射表（基于api return id list.md）
        this.idMappings = {
            // 后台用户映射：邮箱 -> ID
            backendUsers: {
                '<EMAIL>': 37,
                '<EMAIL>': 89,
                '<EMAIL>': 310,
                '<EMAIL>': 311,
                '<EMAIL>': 312,
                'SMW <EMAIL>': 342,
                'SMW <EMAIL>': 343,
                '<EMAIL>': 420,
                '<EMAIL>': 421,
                '空空@gomyhire.com': 777,
                '<EMAIL>': 1047,
                '<EMAIL>': 1181,
                '<EMAIL>': 1201,
                'Swee <EMAIL>': 1652,
                'Skymirror <EMAIL>': 2249,
                '<EMAIL>': 2446,
                '<EMAIL>': 2666
            },
            // 子分类映射
            subCategories: [
                { id: 2, name: 'Pickup' },
                { id: 3, name: 'Dropoff' },
                { id: 4, name: 'Charter' }
            ],
            // 车型映射（基于乘客人数，优先使用5 Seater）
            carTypes: [
                { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', passengerLimit: 3 },
                { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passengerLimit: 10 },
                { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', passengerLimit: 12 },
                { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', passengerLimit: 29 },
                { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', passengerLimit: 43 },
                { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', passengerLimit: 0 },
                { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', passengerLimit: 0 }
            ],
            // 行驶区域映射
            drivingRegions: [
                { id: 1, name: 'Kl/selangor (KL)' },
                { id: 2, name: 'Penang (PNG)' },
                { id: 3, name: 'Johor (JB)' },
                { id: 4, name: 'Sabah (SBH)' },
                { id: 5, name: 'Singapore (SG)' },
                { id: 6, name: '携程专车 (CTRIP)' },
                { id: 8, name: 'Complete (COMPLETE)' },
                { id: 9, name: 'Paging (PG)' },
                { id: 10, name: 'Charter (CHRT)' },
                { id: 12, name: 'Malacca (MLK)' },
                { id: 13, name: 'SMW (SMW)' }
            ],
            // 语言映射
            languages: [
                { id: 2, name: 'English (EN)' },
                { id: 3, name: 'Malay (MY)' },
                { id: 4, name: 'Chinese (CN)' },
                { id: 5, name: 'Paging (PG)' },
                { id: 6, name: 'Charter (CHARTER)' },
                { id: 8, name: '携程司导 (IM)' },
                { id: 9, name: 'PSV (PSV)' },
                { id: 10, name: 'EVP (EVP)' },
                { id: 11, name: 'Car Type Reverify (CAR)' },
                { id: 12, name: 'Jetty (JETTY)' },
                { id: 13, name: 'PhotoSkill Proof (PHOTO)' }
            ]
        };

        this.orderParsingPrompt = `
你是一个高度智能的、专为马来西亚和新加坡设计的用车服务订单处理引擎。
你的 **唯一任务** 是精确地、无逻辑遗漏地解析非结构化订单文本，并根据下方定义的 **五步严格规则**，直接计算并输出一个 **JSON数组**。
即使只有一个订单，也必须以数组形式 \\\`[ { ... } ]\\\` 返回。

---

### **输出格式契约 (JSON Schema)**

你必须严格按照以下JSON结构输出。**所有未在文本中找到对应信息的字段，其值必须为 \\\`null\\\`**，不能省略字段。

\\\`\\\`\\\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null",
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null",
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null",
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "price": "number | null",
    "extra_requirement": "string | null"
  }
]
\\\`\\\`\\\`

---

### **第一步：多订单识别**

-   **核心指令**: 仔细阅读整个文本，判断它是否包含多个独立的订单。独立的订单通常由换行符、分隔线（如"---"）或明确的编号（如“订单1”，“订单2”）分隔。
-   **输出**: 如果识别出多个订单，则在最终的JSON数组中为每个订单创建一个独立的对象。

---

### **第二步：应用核心业务逻辑和计算规则 (对每个订单独立应用)**

**规则1: 服务类型 ('sub_category_id') 自动判断 (带冲突解决)**
你必须根据以下 **优先级顺序** 决定服务类型：
  1.  **包车关键词 (最高优先级)**: 如果包含 "包车", "charter", "全天", "day tour", "多小时", "景点游", "半日游" -> **直接判定为包车 (4)**，并停止后续判断。
  2.  **地点判断**:
      -   如果\\\`pickup\\\`是机场而\\\`dropoff\\\`不是机场 -> **接机 (2)**。
      -   如果\\\`dropoff\\\`是机场而\\\`pickup\\\`不是机场 -> **送机 (3)**。
  3.  **航班类型判断**:
      -   如果 \\\`flight_type\\\` 是 'Arrival' 或文本含 "到达"、"落地" -> **接机 (2)**。
      -   如果 \\\`flight_type\\\` 是 'Departure' 或文本含 "出发"、"起飞" -> **送机 (3)**。
  4.  **通用关键词**:
      -   "接机", "迎接", "airport pickup", "从机场" -> 接机 (2)
      -   "送机", "去机场", "airport dropoff", "到机场" -> 送机 (3)
  5.  **默认值**: 如果以上规则都无法判断，默认为接机 (2)。

**规则2: 接送时间 ('pickup_time') 自动计算 (关键计算)**
你必须计算出最终的 'pickup_time'。
  -   **对于送机 (sub_category_id: 3)**:
      -   'pickup_time' = 'departure_time' 或 'flight_time' **减去 3.5 小时**。
      -   **必须处理跨天**: 例如航班 '02:00'起飞，接送时间是前一天的 '22:30'，此时 **\\\`pickup_date\\\` 也必须相应地调整为前一天**。
  -   **对于接机 (sub_category_id: 2)**:
      -   'pickup_time' = 'arrival_time' 或 'flight_time'。
  -   **对于包车 (sub_category_id: 4)**:
      -   'pickup_time' 等于客户指定的开始时间。
  -   **重要**: 在返回的JSON中，'pickup_time' 必须是**计算后的最终结果**。

**规则3: 车型 ('car_type_id') 智能推荐 (考虑行李)**
根据'passenger_count'和'luggage_count'，从下面的 **车型ID参考表** 中选择**最经济且最合适**的车型ID。
  -   1-3人, 行李≤3: 5 (5 Seater)
  -   4人, 或行李>3: 37 (Extended 5) 或 35 (7 Seater SUV)
  -   5-6人: 15 (7 Seater MPV)
  -   7-10人: 20 (10 Seater MPV/Van)
  -   如果提到 "豪华", "高级", "Velfire", "Alphard" -> 优先考虑 32 (Velfire/Alphard)。

**规则4: 语言 ('languages_id_array') 智能判断 (简化规则)**
  -   如果'customer_name'是中文，或文本中包含中文字符 -> **直接返回 \\\`[4]\\\` (中文)**。
  -   否则，**默认返回 \\\`[2]\\\` (英文)**。

---

### **第三步：数据格式化**

  -   **日期 ('pickup_date')**: 必须是 'YYYY-MM-DD'。必须能处理 "今天", "明天", "后天", "周三", "下周一" 等相对日期，并根据当前日期计算出准确日期。
  -   **时间 ('pickup_time', etc.)**: 必须是 'HH:MM' 24小时制。必须能处理 "早上8点", "下午3:30", "8am", "3:30pm" 等格式。
  -   **价格 ('price')**: 必须是纯数字。如果原文是 "RM150", "$150", "150马币"，必须提取纯数字 \\\`150\\\`。
  -   **布尔值**: 'baby_chair' 等字段必须是 \`true\` 或 \`false\`，而不是字符串。

---

### **第四步：参考ID与地点数据库**

#### **ID参考表**
-   **sub_category_id**: 2:接机, 3:送机, 4:包车
-   **car_type_id**: 5:'5 Seater', 37:'Extended 5', 35:'7 Seater SUV', 15:'7 Seater MPV', 32:'Velfire/Alphard', 20:'10 Seater Van'
-   **driving_region_id**: 1:'KL/Selangor', 2:'Penang', 3:'Johor', 4:'Sabah', 5:'Singapore', 12:'Malacca'
-   **languages_id_array**: 2:'English', 4:'Chinese'

#### **地点数据库 (地点标准化)**
你必须使用此表将文本中的地点名称（特别是酒店）标准化为官方名称。

*   **主要机场**:
    *   \\\`KLIA\\\`, \\\`KUL\\\`: "Kuala Lumpur International Airport (KLIA1)"
    *   \\\`KLIA2\\\`: "Kuala Lumpur International Airport 2 (KLIA2)"
    *   \\\`PEN\\\`: "Penang International Airport"
    *   \\\`JHB\\\`, \\\`Senai\\\`: "Senai International Airport, Johor Bahru"
    *   \\\`BKI\\\`: "Kota Kinabalu International Airport"
    *   \\\`SIN\\\`, \\\`Changi\\\`: "Singapore Changi Airport"
*   **吉隆坡(Kuala Lumpur)酒店**:
    *   \\\`文华东方\\\`, \\\`Mandarin Oriental\\\`: "Mandarin Oriental Kuala Lumpur"
    *   \\\`香格里拉\\\`, \\\`Shangri-La\\\`: "Shangri-La Hotel Kuala Lumpur"
    *   \\\`希尔顿\\\`, \\\`Hilton\\\`: "Hilton Kuala Lumpur"
    *   \\\`逸林\\\`, \\\`DoubleTree\\\`: "DoubleTree by Hilton Kuala Lumpur"
    *   \\\`万豪\\\`, \\\`Marriott\\\`: "JW Marriott Hotel Kuala Lumpur"
    *   \\\`丽思卡尔顿\\\`, \\\`Ritz-Carlton\\\`: "The Ritz-Carlton Kuala Lumpur"
    *   \\\`君悦\\\`, \\\`Grand Hyatt\\\`: "Grand Hyatt Kuala Lumpur"
    *   \\\`四季\\\`, \\\`Four Seasons\\\`: "Four Seasons Hotel Kuala Lumpur"
    *   \\\`双威\\\`, \\\`Sunway\\\`: "Sunway Resort Hotel"
    *   \\\`百乐海\\\`, \\\`PARKROYAL\\\`: "PARKROYAL COLLECTION Kuala Lumpur"
    *   \\\`大华\\\`, \\\`Majestic\\\`: "The Majestic Hotel Kuala Lumpur"
    *   \\\`菲斯\\\`, \\\`FACE\\\`: "THE FACE Style Hotel"
*   **槟城(Penang)酒店**:
    *   \\\`东方大酒店\\\`, \\\`E&O\\\`: "Eastern & Oriental Hotel"
    *   \\\`硬石\\\`, \\\`Hard Rock\\\`: "Hard Rock Hotel Penang"
    *   \\\`香格里拉金沙\\\`, \\\`Golden Sands\\\`: "Shangri-La Golden Sands, Penang"
    *   \\\`百乐海\\\`, \\\`PARKROYAL\\\`: "PARKROYAL Penang Resort"
    *   \\\`蓝屋\\\`, \\\`张弼士\\\`: "Cheong Fatt Tze Mansion"
*   **新山(Johor Bahru)酒店**:
    *   \\\`逸林\\\`, \\\`DoubleTree\\\`: "DoubleTree by Hilton Hotel Johor Bahru"
    *   \\\`万丽\\\`, \\\`Renaissance\\\`: "Renaissance Johor Bahru Hotel"
    *   \\\`乐高\\\`, \\\`Legoland\\\`: "Legoland Hotel Malaysia"
*   **亚庇(Kota Kinabalu)酒店**:
    *   \\\`丹绒亚路香格里拉\\\`, \\\`Tanjung Aru\\\`: "Shangri-La Tanjung Aru, Kota Kinabalu"
    *   \\\`沙利雅香格里拉\\\`, \\\`Rasa Ria\\\`: "Shangri-La Rasa Ria, Kota Kinabalu"
    *   \\\`麦哲伦\\\`, \\\`Magellan\\\`: "The Magellan Sutera Resort"
    *   \\\`太平洋\\\`, \\\`Pacific\\\`: "The Pacific Sutera Hotel"
    *   \\\`凯悦\\\`, \\\`Hyatt\\\`: "Hyatt Regency Kinabalu"
*   **新加坡(Singapore)酒店**:
    *   \\\`滨海湾金沙\\\`, \\\`MBS\\\`: "Marina Bay Sands"
    *   \\\`莱佛士\\\`, \\\`Raffles\\\`: "Raffles Singapore"
    *   \\\`文华东方\\\`, \\\`Mandarin Oriental\\\`: "Mandarin Oriental Singapore"
    *   \\\`老板酒店\\\`, \\\`Hotel Boss\\\`: "Hotel Boss"

---

### **第五步：查看示例**

**示例1 (送机 - 计算时间并调整日期):**
"王先生, 明早航班MH123 02:00从KLIA起飞, 请从KL Hilton送机"
→ **JSON输出:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "王先生",
    "customer_contact": null,
    "sub_category_id": 3,
    "flight_info": "MH123",
    "departure_time": "02:00",
    "arrival_time": null,
    "flight_type": "Departure",
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "22:30",
    "pickup": "Hilton Kuala Lumpur",
    "dropoff": "Kuala Lumpur International Airport (KLIA1)",
    "passenger_count": 1,
    "luggage_count": null,
    "car_type_id": 5,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "price": null,
    "extra_requirement": null
  }
]
\\\`\\\`\\\`

**示例2 (多订单解析):**
"订单1: 李女士, 13800138000, 明天下午3点半CZ351降落KLIA2, 4大1小, 需要儿童座椅, 到Sunway酒店. 订单2: 同一个客户, 后天早上9点从Sunway酒店包车8小时, 中文司机"
→ **JSON输出:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "李女士",
    "customer_contact": "13800138000",
    "sub_category_id": 2,
    "flight_info": "CZ351",
    "arrival_time": "15:30",
    "departure_time": null,
    "flight_type": "Arrival",
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "15:30",
    "pickup": "Kuala Lumpur International Airport 2 (KLIA2)",
    "dropoff": "Sunway Resort Hotel",
    "passenger_count": 5,
    "luggage_count": null,
    "car_type_id": 15,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "baby_chair": true,
    "price": null,
    "extra_requirement": null
  },
  {
    "customer_name": "李女士",
    "customer_contact": "13800138000",
    "sub_category_id": 4,
    "flight_info": null,
    "arrival_time": null,
    "departure_time": null,
    "flight_type": null,
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "09:00",
    "pickup": "Sunway Resort Hotel",
    "dropoff": null,
    "passenger_count": 5,
    "luggage_count": null,
    "car_type_id": 15,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "baby_chair": false,
    "price": null,
    "extra_requirement": "包车8小时, 中文司机"
  }
]
\\\`\\\`\\\`

---

**最终指令：**
现在，请严格按照以上所有规则，分析给定的订单描述，并 **只输出一个符合规范的、完整的JSON数组**。
        `;
    }
    
    /**
     * 对Gemini返回的原始数据进行后期处理和规范化
     * @param {string} rawText - 从Gemini API返回的原始文本
     * @returns {object|null} - 解析和清理后的JSON对象，或在失败时返回null
     */
    postProcessParsedData(rawText) {
        if (!rawText) {
            getLogger().log('Gemini后处理失败：输入文本为空', 'error');
            return null;
        }

        // 尝试从Markdown代码块中提取JSON
        const jsonMatch = rawText.match(/```(json)?\s*([\s\S]*?)\s*```/);
        const jsonString = jsonMatch ? jsonMatch[2].trim() : rawText.trim();

        try {
            let data = JSON.parse(jsonString);
            
            // 新的返回格式是数组，确保后续处理能兼容
            // 如果不是数组，为了兼容旧格式，将其包装成数组
            if (!Array.isArray(data)) {
                data = [data];
            }
            
            // 对数组中的每个订单对象进行格式规范化
            const processedData = data.map(order => this.normalizeDataFormats(order));
            
            getLogger().log('Gemini后处理成功', 'success', { count: processedData.length, data: processedData });
            return processedData;

        } catch (error) {
            getLogger().log('JSON解析失败', 'error', { 
                originalText: rawText,
                jsonString: jsonString,
                error: error.message 
            });
            // 尝试进行更宽松的修复性解析
            return this.basicParse(jsonString);
        }
    }

    /**
     * 规范化单个订单对象中的数据格式
     * @param {object} data - 单个订单对象
     * @returns {object} - 格式规范化后的订单对象
     */
    normalizeDataFormats(data) {
        const normalizedData = { ...data };

        // 规范化电话号码
        if (normalizedData.customer_contact) {
            normalizedData.customer_contact = this.normalizePhoneNumber(normalizedData.customer_contact);
        }
        
        // 规范化日期
        if (normalizedData.pickup_date) {
            normalizedData.pickup_date = this.normalizeDate(normalizedData.pickup_date);
        }

        // 规范化时间
        if (normalizedData.pickup_time) {
            normalizedData.pickup_time = this.normalizeTime(normalizedData.pickup_time);
        }
        if (normalizedData.flight_time) {
            normalizedData.flight_time = this.normalizeTime(normalizedData.flight_time);
        }
        if (normalizedData.departure_time) {
            normalizedData.departure_time = this.normalizeTime(normalizedData.departure_time);
        }
        if (normalizedData.arrival_time) {
            normalizedData.arrival_time = this.normalizeTime(normalizedData.arrival_time);
        }

        // 规范化地点
        if (normalizedData.pickup) {
            normalizedData.pickup = this.normalizeLocation(normalizedData.pickup);
        }
        if (normalizedData.dropoff) {
            normalizedData.dropoff = this.normalizeLocation(normalizedData.dropoff);
        }

        // 确保数字段为数字类型
        const numericFields = ['passenger_count', 'luggage_count', 'price'];
        numericFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = parseInt(normalizedData[field], 10) || null;
            }
        });

        // 确保ID字段为数字
        const idFields = ['sub_category_id', 'car_type_id', 'driving_region_id'];
        idFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = parseInt(normalizedData[field], 10) || null;
            }
        });

        // 确保布尔字段为布尔类型
        const booleanFields = ['baby_chair', 'tour_guide', 'meet_and_greet'];
        booleanFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = String(normalizedData[field]).toLowerCase() === 'true';
            }
        });
        
        // 确保语言ID数组为数组
        if (normalizedData.languages_id_array && !Array.isArray(normalizedData.languages_id_array)) {
            // 尝试从字符串（如"[4]"）转换
            try {
                const parsedArray = JSON.parse(normalizedData.languages_id_array);
                if(Array.isArray(parsedArray)) {
                    normalizedData.languages_id_array = parsedArray;
                }
            } catch (e) {
                // 如果解析失败，则将其包装在数组中
                normalizedData.languages_id_array = [parseInt(normalizedData.languages_id_array, 10) || 2];
            }
        }


        return normalizedData;
    }
    
    /**
     * 规范化电话号码（移除+、-、空格）
     * @param {string} phone - 原始电话号码
     * @returns {string} - 清理后的电话号码
     */
    normalizePhoneNumber(phone) {
        if (!phone || typeof phone !== 'string') return '';
        return phone.replace(/[+\-\s]/g, '');
    }

    /**
     * 规范化日期格式为 YYYY-MM-DD
     * @param {string} date - 原始日期字符串
     * @returns {string} - YYYY-MM-DD格式的日期
     */
    normalizeDate(date) {
        if (!date || typeof date !== 'string') return '';
        try {
            // 尝试直接解析
            const d = new Date(date);
            if (!isNaN(d.getTime())) {
                return d.toISOString().split('T')[0];
            }
        } catch (e) {
            // 失败则返回原始值
            return date;
        }
        return date;
    }
    
    /**
     * 规范化时间格式为 HH:MM
     * @param {string} time - 原始时间字符串
     * @returns {string} - HH:MM格式的时间
     */
    normalizeTime(time) {
        if (!time || typeof time !== 'string') return '';
        const match = time.match(/(\d{1,2}):(\d{2})/);
        if (match) {
            const hour = match[1].padStart(2, '0');
            const minute = match[2];
            return `${hour}:${minute}`;
        }
        return time; // 格式不符则返回原值
    }

    /**
     * 规范化地点名称（移除潜在的星号、多余空格）
     * @param {string} location - 原始地点字符串
     * @returns {string} - 清理后的地点字符串
     */
    normalizeLocation(location) {
        if (!location || typeof location !== 'string') return '';
        return location.replace(/\*/g, '').trim();
    }
    
    /**
     * 从解析数据中计算置信度分数
     * @param {object} data - 解析后的数据
     * @returns {number} - 0到1之间的置信度分数
     */
    calculateConfidence(data) {
        if (!data || typeof data !== 'object') return 0;

        const totalFields = Object.keys(data).length;
        if (totalFields === 0) return 0;

        let filledFields = 0;
        for (const key in data) {
            if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                filledFields++;
            }
        }
        return filledFields / totalFields;
    }

    /**
     * 基础的、更宽松的JSON解析尝试
     * @param {string} text - 可能不完全是JSON的字符串
     * @returns {object|null} - 解析后的对象或null
     */
    basicParse(text) {
        try {
            // 这是一个非常基础的实现，未来可以扩展为更复杂的修复逻辑
            // 例如，尝试添加缺失的括号、引号等
            const sanitized = text
                .replace(/,\s*}/g, '}')
                .replace(/,\s*]/g, ']');
            
            let data = JSON.parse(sanitized);
             if (!Array.isArray(data)) {
                data = [data];
            }
            return data;
        } catch (error) {
            getLogger().log('基础JSON解析也失败', 'error', { text: text, error: error.message });
            return null;
        }
    }

    /**
     * 模拟网络延迟
     * @param {number} ms - 延迟毫秒数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取当前分析状态
     * @returns {object}
     */
    getStatus() {
        return {
            isAnalyzing: this.analysisState.isAnalyzing,
            lastAnalyzedText: this.analysisState.lastAnalyzedText
        };
    }
    
    /**
     * 生成一个随机的示例订单，用于测试
     * @returns {string} - 示例订单描述
     */
    generateSampleOrder() {
        const samples = [
            `客户：张三先生 +60123456789\n接送：KLIA2机场 到 Pavilion KL购物中心\n时间：明天 14:30\n人数：2大1小\n要求：需要儿童座椅`,
            `预订人：李小姐 (<EMAIL>)\n航班：MH370 15:45抵达\n从：吉隆坡国际机场\n到：Bukit Bintang武吉免登\n日期：后天下午4点\n乘客：3人 + 2件大行李\n特殊要求：司机能说中文`,
            `Customer: John Smith\nFrom: KL Sentral Station\nTo: Genting Highlands\nDate:下周三 09:00\nPassengers: 4 adults\nLuggage: 3 large suitcases\nService: Charter tour (8 hours)`,
            `订单1: 刘女士, 13912345678, 明天下午3点半航班CZ351降落KLIA2, 4大1小, 需要儿童座椅, 到Sunway酒店.\n---\n订单2: 同一个客户, 后天早上9点从Sunway酒店包车8小时, 中文司机, 需要去双子塔和独立广场`
        ];
        
        return samples[Math.floor(Math.random() * samples.length)];
    }

    /**
     * 更新Gemini内部的ID映射表（例如，从API获取最新数据后）
     * @param {object} systemData - 包含最新ID映射的对象
     */
    updateIdMappings(systemData) {
        try {
            if (systemData.backend_users) {
                this.idMappings.backendUsers = systemData.backend_users.reduce((acc, user) => {
                    acc[user.email] = user.id;
                    return acc;
                }, {});
            }
            if (systemData.sub_categories) {
                this.idMappings.subCategories = systemData.sub_categories;
            }
            if (systemData.car_types) {
                this.idMappings.carTypes = systemData.car_types.map(car => ({
                    id: car.id,
                    name: car.name,
                    passengerLimit: car.max_passenger_capacity || 0
                }));
            }
            if (systemData.driving_regions) {
                this.idMappings.drivingRegions = systemData.driving_regions;
            }
            if (systemData.languages) {
                this.idMappings.languages = systemData.languages;
            }
            getLogger().log('Gemini ID 映射已更新', 'info', this.idMappings);
        } catch (err) {
            getLogger().logError('同步 Gemini ID 映射失败', err);
        }
    }
    
    /**
     * 动态配置实时分析参数
     * @function
     * @param {Object} config - 实时分析配置项
     * @returns {void}
     */
    configureRealtimeAnalysis(config) {
        // 参数校验，确保传入为对象
        if (typeof config !== 'object' || config === null) return;
        // 合并配置到realtimeConfig
        this.realtimeConfig = { ...this.realtimeConfig, ...config };
    }

    /**
     * 动态配置实时分析参数（别名方法，供旧版本调用）
     * @function
     * @param {Object} config - 实时分析配置项
     * @returns {void}
     */
    setRealtimeAnalysis(config) {
        // 调用新的配置方法
        this.configureRealtimeAnalysis(config);
    }

    /**
     * 判断Gemini服务是否可用
     * @returns {boolean}
     */
    isAvailable() {
        // 只要apiKey存在且非空字符串即认为可用
        return typeof this.apiKey === 'string' && this.apiKey.trim().length > 0;
    }

    /**
     * 核心函数：解析订单文本
     * @param {string} text - 用户输入的订单描述
     * @param {boolean} isRealtime - 是否为实时分析模式
     * @returns {Promise<object|null>} - 解析后的数据或null
     */
    async parseOrder(text, isRealtime = false) {
        if (!text || text.length < (isRealtime ? this.realtimeConfig.minInputLength : 10)) {
            return null;
        }

        this.analysisState.isAnalyzing = true;
        this.analysisState.lastAnalyzedText = text;

        const requestBody = {
            contents: [{
                parts: [{ text: this.orderParsingPrompt + "\n\n**订单描述:**\n" + text }]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 1,
                topP: 1,
                maxOutputTokens: 2048,
                stopSequences: []
            },
            safetySettings: [
                // 调整安全设置以适应业务场景
                { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' }
            ]
        };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            this.analysisState.currentRequest = controller;

            const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorBody = await response.json();
                throw new Error(`API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
            }

            const data = await response.json();
            const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text;
            
            if (!rawText) {
                throw new Error('API返回内容为空或格式不正确');
            }

            const processedData = this.postProcessParsedData(rawText);
            this.analysisState.isAnalyzing = false;
            return processedData;

        } catch (error) {
            this.analysisState.isAnalyzing = false;
            if (error.name === 'AbortError') {
                getLogger().log('Gemini请求超时', 'error');
            } else {
                getLogger().logError('Gemini解析时发生严重错误', error);
            }
            return null;
        }
    }

    /**
     * 取消当前分析请求
     * @function
     */
    cancelCurrentAnalysis() {
        // 如果当前有分析请求（AbortController），则中断
        if (this.analysisState.currentRequest && typeof this.analysisState.currentRequest.abort === 'function') {
            this.analysisState.currentRequest.abort();
            this.analysisState.currentRequest = null;
        }
    }
} // GeminiService Class End

    // 创建全局Gemini服务实例
    const geminiService = new GeminiService();
    
    // 将实例暴露到全局OTA命名空间
    window.OTA.geminiService = geminiService;

    // 为了向后兼容，也暴露到全局window
    window.geminiService = geminiService;

})();