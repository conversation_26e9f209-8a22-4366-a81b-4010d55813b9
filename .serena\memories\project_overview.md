# OTA订单处理系统 - 项目概览

## 项目目的
OTA订单处理系统是一个基于Web的智能订单管理平台，专门为GoMyHire公司设计，用于处理来自各种OTA（Online Travel Agency）平台的订单。系统集成了Gemini AI服务，提供智能订单解析和自动化处理功能。

## 核心功能
1. **智能订单解析** - 使用Gemini AI实时分析订单描述，自动提取关键信息
2. **GoMyHire API集成** - 与GoMyHire后端系统完全集成，支持订单创建和数据同步
3. **多账号支持** - 支持多个操作员账号登录和管理
4. **实时状态监控** - 提供系统状态、API连接状态的实时监控
5. **移动端优化** - 包含移动端友好的订单预览界面

## 技术特点
- **模块化架构** - 采用ES6模块化设计，代码结构清晰
- **实时分析** - 支持输入内容的实时AI分析，无需手动触发
- **状态持久化** - 登录状态和系统数据的本地缓存
- **错误恢复** - 完善的错误处理和恢复机制
- **性能优化** - 包含防抖、节流等性能优化措施

## 主要用户
- GoMyHire操作员
- 订单处理专员
- 系统管理员