# 编码规范和约定

## 命名约定
- **文件名**: kebab-case (例如: api-service.js)
- **类名**: PascalCase (例如: ApiService)
- **函数名**: camelCase (例如: getUserData)
- **变量名**: camelCase (例如: currentUser)
- **常量名**: UPPER_SNAKE_CASE (例如: API_BASE_URL)

## 代码风格
- **缩进**: 4个空格
- **字符串**: 优先使用单引号，模板字符串使用反引号
- **分号**: 必须使用分号结尾
- **括号**: 函数参数、条件语句等使用空格分隔

## 注释规范
- **JSDoc**: 所有导出的类、方法必须有完整的JSDoc注释
- **中文注释**: 复杂逻辑使用中文行内注释解释
- **文件头注释**: 每个文件包含用途说明和作者信息

## JSDoc示例
```javascript
/**
 * 用户认证服务
 * @class AuthService
 */
class AuthService {
    /**
     * 用户登录
     * @param {string} email - 用户邮箱
     * @param {string} password - 用户密码
     * @returns {Promise<object>} 登录结果
     */
    async login(email, password) {
        // 实现逻辑
    }
}
```

## 错误处理
- **统一错误处理**: 使用Logger模块记录所有错误
- **用户友好**: 向用户显示友好的错误信息
- **防御性编程**: 对所有外部输入进行验证

## 性能要求
- **单文件限制**: 每个文件不超过500行代码
- **防抖节流**: 用户输入和API调用使用防抖/节流
- **内存管理**: 及时清理事件监听器和定时器