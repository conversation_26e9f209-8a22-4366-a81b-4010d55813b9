# 技术栈

## 前端技术
- **HTML5** - 语义化标记，支持现代浏览器特性
- **CSS3** - 使用CSS变量系统，支持暗色主题
- **JavaScript ES6+** - 原生JavaScript，使用ES6模块系统
- **Vite** - 开发服务器和构建工具

## 核心依赖
- **Google Gemini API** - AI智能分析服务
- **GoMyHire API** - 后端业务系统集成
- **Web APIs** - Fetch API、LocalStorage、Clipboard API等

## 架构模式
- **模块化设计** - 每个功能模块独立，便于维护
- **事件驱动** - 基于事件的组件通信
- **状态管理** - 集中式状态管理（AppState）
- **服务层模式** - API服务、AI服务等独立封装

## 开发工具
- **Vite** - 快速开发服务器
- **ES6 Modules** - 原生模块系统，无需打包工具
- **Browser DevTools** - 调试和性能分析

## 部署环境
- **静态文件服务** - 可部署到任何静态文件服务器
- **HTTPS要求** - 需要HTTPS环境以支持现代Web API