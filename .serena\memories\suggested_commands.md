# 建议的开发命令

## 开发环境
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## Windows系统命令
```cmd
# 查看文件列表
dir

# 查看目录结构
tree /f

# 查找文件
findstr /s /i "关键词" *.js

# 复制文件
copy source.js destination.js

# 删除文件
del filename.js

# 创建目录
mkdir new-folder
```

## Git操作
```bash
# 查看状态
git status

# 添加文件
git add .

# 提交更改
git commit -m "描述信息"

# 推送到远程
git push origin main

# 拉取最新代码
git pull origin main
```

## 调试命令
```javascript
// 浏览器控制台调试
console.log(window.app.getStatus());
console.log(window.appState.getState());
console.log(window.logger.getLogs());

// 性能监控
window.utils.performanceMonitor.getStats();

// 清理缓存
localStorage.clear();
```

## 测试相关
```bash
# 启动本地服务器测试
npx serve .

# 检查代码语法
npx eslint js/*.js

# 格式化代码
npx prettier --write js/*.js
```

## API测试
```bash
# 使用curl测试API
curl -X POST https://gomyhire.com.my/api/login -H "Content-Type: application/json" -d "{\"email\":\"<EMAIL>\",\"password\":\"Gomyhire@123456\"}"
```