# 代码结构

## 目录结构
```
create job/
├── js/                     # JavaScript模块目录
│   ├── app-state.js       # 应用状态管理
│   ├── api-service.js     # API服务模块
│   ├── gemini-service.js  # Gemini AI服务
│   ├── ui-manager.js      # UI管理模块
│   ├── logger.js          # 日志记录模块
│   └── utils.js           # 工具函数集合
├── public/                # 静态资源
├── index.html            # 主页面
├── main.js               # 应用入口文件
├── style.css             # 主样式文件
├── API List to create order.txt  # API文档
├── api return id list.md # API数据映射
├── GEMINI_CONFIG_UPDATE.md # 配置更新文档
└── package.json          # 项目配置
```

## 核心模块说明

### AppState (app-state.js)
- 集中式状态管理
- 本地存储持久化
- 状态变更监听机制

### ApiService (api-service.js)
- GoMyHire API集成
- 认证管理
- 数据缓存和降级机制

### GeminiService (gemini-service.js)
- Gemini AI API集成
- 实时订单解析
- 智能内容分析

### UIManager (ui-manager.js)
- DOM操作管理
- 事件处理
- 界面状态同步

### Logger (logger.js)
- 统一日志记录
- 多级别日志支持
- 用户行为追踪

### Utils (utils.js)
- 通用工具函数
- 性能监控
- 数据处理辅助函数

## 模块依赖关系
- main.js → 所有模块（应用入口）
- ui-manager.js → app-state.js, logger.js
- api-service.js → app-state.js, logger.js
- gemini-service.js → app-state.js, logger.js

## 清理说明
项目已完成代码清理，移除了以下不必要的文件：
- 测试文件：gemini-config-test.html
- 模板遗留：counter.js, javascript.svg
- 重复功能：mobile-order-preview相关文件
- 紧凑版本：order-preview-compact相关文件

保持了核心功能的完整性和项目结构的整洁性。